<?php

namespace Modules\Role\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedRolePermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed role module permissions.
     */
    private function seedRolePermissions(): void
    {
        $permissions = [
            // Role Management Permissions (using existing naming convention)
            [
                'name' => 'role.view',
                'display_name' => 'View Roles',
                'description' => 'Permission to view roles list and details',
                'module_name' => 'role',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'role.create',
                'display_name' => 'Create Roles',
                'description' => 'Permission to create new roles',
                'module_name' => 'role',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'role.edit',
                'display_name' => 'Edit Roles',
                'description' => 'Permission to update existing roles',
                'module_name' => 'role',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'role.delete',
                'display_name' => 'Delete Roles',
                'description' => 'Permission to soft delete roles',
                'module_name' => 'role',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'role.destroy',
                'display_name' => 'Destroy Roles',
                'description' => 'Permission to permanently delete roles',
                'module_name' => 'role',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            
            // Permission Management Permissions
            [
                'name' => 'permission.view',
                'display_name' => 'View Permissions',
                'description' => 'Permission to view permissions list and details',
                'module_name' => 'role',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'permission.create',
                'display_name' => 'Create Permissions',
                'description' => 'Permission to create new permissions',
                'module_name' => 'role',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'permission.edit',
                'display_name' => 'Edit Permissions',
                'description' => 'Permission to update existing permissions',
                'module_name' => 'role',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
            [
                'name' => 'permission.delete',
                'display_name' => 'Delete Permissions',
                'description' => 'Permission to delete permissions',
                'module_name' => 'role',
                'sort_order' => 9,
                'guard_name' => 'api',
            ],
            
            // Role-Permission Assignment Permissions
            [
                'name' => 'role.assign-permissions',
                'display_name' => 'Assign Permissions to Roles',
                'description' => 'Permission to assign/revoke permissions to/from roles',
                'module_name' => 'role',
                'sort_order' => 10,
                'guard_name' => 'api',
            ],
            [
                'name' => 'user.assign-roles',
                'display_name' => 'Assign Roles to Users',
                'description' => 'Permission to assign/revoke roles to/from users',
                'module_name' => 'role',
                'sort_order' => 11,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign role permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $rolePermissions = Permission::where('module_name', 'role')->where('guard_name', 'api')->get();
            
            foreach ($rolePermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
