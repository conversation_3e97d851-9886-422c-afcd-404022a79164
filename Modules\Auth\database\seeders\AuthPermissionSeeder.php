<?php

namespace Modules\Auth\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AuthPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedAuthPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed auth module permissions.
     */
    private function seedAuthPermissions(): void
    {
        $permissions = [
            [
                'name' => 'auth.profile.read',
                'display_name' => 'View Profile',
                'description' => 'Permission to view user profile information',
                'module_name' => 'auth',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'auth.profile.update',
                'display_name' => 'Update Profile',
                'description' => 'Permission to update user profile information',
                'module_name' => 'auth',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'auth.password.change',
                'display_name' => 'Change Password',
                'description' => 'Permission to change user password',
                'module_name' => 'auth',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'auth.oauth.manage',
                'display_name' => 'Manage OAuth Accounts',
                'description' => 'Permission to link/unlink OAuth provider accounts',
                'module_name' => 'auth',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'auth.session.manage',
                'display_name' => 'Manage Sessions',
                'description' => 'Permission to manage user sessions and tokens',
                'module_name' => 'auth',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'auth.rate-limit.view',
                'display_name' => 'View Rate Limits',
                'description' => 'Permission to view authentication rate limiting information',
                'module_name' => 'auth',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
            [
                'name' => 'auth.lockout.manage',
                'display_name' => 'Manage Account Lockouts',
                'description' => 'Permission to manage account lockouts and unlock accounts',
                'module_name' => 'auth',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign auth permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $authPermissions = Permission::where('module_name', 'auth')->where('guard_name', 'api')->get();
            
            foreach ($authPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
