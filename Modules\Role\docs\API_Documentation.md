# Role Module API Documentation

## Base URL
```
/api/v1/auth/roles
```

## Authentication
All endpoints require JWT authentication:
```
Authorization: Bearer {jwt_token}
```

---

## 1. GET /api/v1/auth/roles (List Roles)

### Request
```http
GET /api/v1/auth/roles
Authorization: Bearer {jwt_token}
Content-Type: application/json

Query Parameters:
- limit: integer (optional, default: 10) - Items per page
- page: integer (optional, default: 1) - Current page
- search: string (optional) - Search by name/display_name
- status: string (optional) - active|inactive
- module_name: string (optional) - Filter by module
- sort_by: string (optional) - name|display_name|priority|created_at
- sort_direction: string (optional) - asc|desc
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Roles retrieved successfully.",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "name": "super-admin",
                "display_name": "Super Administrator",
                "description": "Super administrator with full system access",
                "guard_name": "api",
                "status": "active",
                "priority": 100,
                "is_system": true,
                "created_at": "2025-01-07T10:30:00.000000Z",
                "updated_at": "2025-01-07T10:30:00.000000Z"
            },
            {
                "id": 2,
                "name": "admin",
                "display_name": "Administrator",
                "description": "Administrator with most system permissions",
                "guard_name": "api",
                "status": "active",
                "priority": 90,
                "is_system": true,
                "created_at": "2025-01-07T10:30:00.000000Z",
                "updated_at": "2025-01-07T10:30:00.000000Z"
            }
        ],
        "first_page_url": "http://localhost:8000/api/v1/auth/roles?page=1",
        "from": 1,
        "last_page": 1,
        "last_page_url": "http://localhost:8000/api/v1/auth/roles?page=1",
        "links": [
            {
                "url": null,
                "label": "&laquo; Previous",
                "active": false
            },
            {
                "url": "http://localhost:8000/api/v1/auth/roles?page=1",
                "label": "1",
                "active": true
            },
            {
                "url": null,
                "label": "Next &raquo;",
                "active": false
            }
        ],
        "next_page_url": null,
        "path": "http://localhost:8000/api/v1/auth/roles",
        "per_page": 10,
        "prev_page_url": null,
        "to": 2,
        "total": 2
    }
}
```

---

## 2. POST /api/v1/auth/roles (Create Role)

### Request
```http
POST /api/v1/auth/roles
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "name": "editor-role",
    "display_name": "Content Editor",
    "description": "Role for content editors",
    "guard_name": "api",
    "status": "active",
    "priority": 50,
    "is_system": false,
    "permissions": [1, 2, 3]
}
```

### Validation Rules
```php
- name: required|string|max:255|alpha_dash|unique:roles
- display_name: required|string|max:255
- description: nullable|string|max:1000
- guard_name: nullable|string|max:255
- priority: nullable|integer|min:0|max:1000
- is_system: nullable|boolean
- status: required|string|in:active,inactive
- permissions: nullable|array
- permissions.*: integer|exists:permissions,id
```

### Response Success (201)
```json
{
    "success": true,
    "message": "Role created successfully.",
    "data": {
        "id": 6,
        "name": "editor-role",
        "display_name": "Content Editor",
        "description": "Role for content editors",
        "guard_name": "api",
        "status": "active",
        "priority": 50,
        "is_system": false,
        "permissions": [
            {
                "id": 1,
                "name": "content.view",
                "display_name": "View Content"
            }
        ],
        "created_at": "2025-01-07T11:00:00.000000Z",
        "updated_at": "2025-01-07T11:00:00.000000Z"
    }
}
```

### Response Error (422)
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "name": ["A role with this name already exists."],
        "display_name": ["The display name field is required."]
    }
}
```

---

## 3. GET /api/v1/auth/roles/{id} (Show Role)

### Request
```http
GET /api/v1/auth/roles/1
Authorization: Bearer {jwt_token}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Role retrieved successfully.",
    "data": {
        "id": 1,
        "name": "super-admin",
        "display_name": "Super Administrator",
        "description": "Super administrator with full system access",
        "guard_name": "api",
        "status": "active",
        "priority": 100,
        "is_system": true,
        "permissions": [
            {
                "id": 1,
                "name": "role.view",
                "display_name": "View Roles",
                "module_name": "role"
            }
        ],
        "users": [
            {
                "id": 1,
                "username": "admin",
                "first_name": "Admin",
                "last_name": "User"
            }
        ],
        "created_at": "2025-01-07T10:30:00.000000Z",
        "updated_at": "2025-01-07T10:30:00.000000Z"
    }
}
```

---

## 4. PUT /api/v1/auth/roles/{id} (Update Role)

### Request
```http
PUT /api/v1/auth/roles/1
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "name": "updated-role",
    "display_name": "Updated Role Name",
    "description": "Updated description",
    "status": "active",
    "priority": 60,
    "permissions": [1, 2, 4]
}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Role updated successfully.",
    "data": {
        "id": 1,
        "name": "updated-role",
        "display_name": "Updated Role Name",
        "description": "Updated description",
        "guard_name": "api",
        "status": "active",
        "priority": 60,
        "is_system": false,
        "permissions": [
            {
                "id": 1,
                "name": "role.view",
                "display_name": "View Roles"
            }
        ],
        "created_at": "2025-01-07T10:30:00.000000Z",
        "updated_at": "2025-01-07T11:15:00.000000Z"
    }
}
```

---

## 5. DELETE /api/v1/auth/roles/{id}/delete (Soft Delete)

### Request
```http
DELETE /api/v1/auth/roles/1/delete
Authorization: Bearer {jwt_token}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Role deleted successfully.",
    "data": null
}
```

### Response Error (422)
```json
{
    "success": false,
    "message": "Cannot delete this role. It may be a system role or have assigned users.",
    "data": null
}
```

---

## 6. DELETE /api/v1/auth/roles/{id}/destroy (Permanent Delete)

### Request
```http
DELETE /api/v1/auth/roles/1/destroy
Authorization: Bearer {jwt_token}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Role permanently deleted successfully.",
    "data": 1
}
```

### Response Error (422)
```json
{
    "success": false,
    "message": "Cannot permanently delete this role. It may be a system role or have assigned users.",
    "data": null
}
```

---

## 7. PUT /api/v1/auth/roles/{id}/restore (Restore Role)

### Request
```http
PUT /api/v1/auth/roles/1/restore
Authorization: Bearer {jwt_token}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Role restored successfully.",
    "data": {
        "id": 1,
        "name": "restored-role",
        "display_name": "Restored Role",
        "status": "active",
        "deleted_at": null,
        "created_at": "2025-01-07T10:30:00.000000Z",
        "updated_at": "2025-01-07T11:20:00.000000Z"
    }
}
```

---

## 8. DELETE /api/v1/auth/roles/bulk/delete (Bulk Soft Delete)

### Request
```http
DELETE /api/v1/auth/roles/bulk/delete
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "ids": [1, 2, 3, 4, 5]
}
```

### Validation Rules
```php
- ids: required|array|min:1
- ids.*: required|integer|exists:roles,id
```

### Response Success (200)
```json
{
    "success": true,
    "message": "3 roles deleted successfully. Some roles could not be deleted: Role 'Super Administrator' cannot be deleted.",
    "data": {
        "deleted_count": 3,
        "errors": [
            "Role 'Super Administrator' cannot be deleted."
        ]
    }
}
```

---

## 9. DELETE /api/v1/auth/roles/bulk/destroy (Bulk Permanent Delete)

### Request
```http
DELETE /api/v1/auth/roles/bulk/destroy
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "ids": [1, 2, 3]
}
```

### Validation Rules
```php
- ids: required|array|min:1
- ids.*: required|integer|exists:roles,id,deleted_at,NOT_NULL (only soft deleted roles)
```

### Response Success (200)
```json
{
    "success": true,
    "message": "2 roles permanently deleted.",
    "data": {
        "deleted_count": 2,
        "errors": []
    }
}
```

---

## 10. PUT /api/v1/auth/roles/bulk/restore (Bulk Restore)

### Request
```http
PUT /api/v1/auth/roles/bulk/restore
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "ids": [1, 2, 3]
}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Roles restored successfully.",
    "data": {
        "restored_count": 3
    }
}
```

---

## 11. GET /api/v1/auth/roles/dropdown (Dropdown List)

### Request
```http
GET /api/v1/auth/roles/dropdown
Authorization: Bearer {jwt_token}
```

### Response Success (200)
```json
{
    "success": true,
    "message": "Roles retrieved successfully.",
    "data": [
        {
            "id": 1,
            "name": "super-admin",
            "display_name": "Super Administrator",
            "priority": 100
        },
        {
            "id": 2,
            "name": "admin",
            "display_name": "Administrator",
            "priority": 90
        }
    ]
}
```

---

## 🔒 Common Error Responses

### 401 Unauthorized
```json
{
    "success": false,
    "message": "Unauthenticated.",
    "data": null
}
```

### 403 Forbidden
```json
{
    "success": false,
    "message": "This action is unauthorized.",
    "data": null
}
```

### 404 Not Found
```json
{
    "success": false,
    "message": "Role not found.",
    "data": null
}
```

### 500 Server Error
```json
{
    "success": false,
    "message": "Failed to create role.",
    "data": "Detailed error message"
}
```

---

## 📋 Permission Requirements

### Middleware Applied
```php
- role.view: index, show, dropdown
- role.create: store
- role.edit: update
- role.delete: delete, restore, bulkDelete, bulkRestore
- role.destroy: destroy, bulkDestroy
```

### Available Permissions
- `role.view` - View roles list and details
- `role.create` - Create new roles
- `role.edit` - Update existing roles
- `role.delete` - Soft delete and restore roles
- `role.destroy` - Permanently delete roles
- `permission.view` - View permissions
- `permission.create` - Create permissions
- `permission.edit` - Update permissions
- `permission.delete` - Delete permissions
- `role.assign-permissions` - Assign permissions to roles
- `user.assign-roles` - Assign roles to users

**Note:** `super-admin` role has access to all endpoints regardless of specific permissions.
```
