<?php

namespace Modules\ActiveCode\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ActiveCodePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedActiveCodePermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed active code module permissions.
     */
    private function seedActiveCodePermissions(): void
    {
        $permissions = [
            [
                'name' => 'activecode.read',
                'display_name' => 'View Active Codes',
                'description' => 'Permission to view active codes list and details',
                'module_name' => 'activecode',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'activecode.create',
                'display_name' => 'Generate Active Codes',
                'description' => 'Permission to generate new verification codes',
                'module_name' => 'activecode',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'activecode.verify',
                'display_name' => 'Verify Active Codes',
                'description' => 'Permission to verify active codes',
                'module_name' => 'activecode',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'activecode.resend',
                'display_name' => 'Resend Active Codes',
                'description' => 'Permission to resend verification codes',
                'module_name' => 'activecode',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'activecode.cleanup',
                'display_name' => 'Cleanup Active Codes',
                'description' => 'Permission to cleanup expired active codes',
                'module_name' => 'activecode',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'activecode.settings',
                'display_name' => 'Manage Active Code Settings',
                'description' => 'Permission to manage active code configuration settings',
                'module_name' => 'activecode',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign active code permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();
        
        if ($superAdmin) {
            $activeCodePermissions = Permission::where('module_name', 'activecode')->where('guard_name', 'api')->get();
            
            foreach ($activeCodePermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
